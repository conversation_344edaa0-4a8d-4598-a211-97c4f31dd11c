package com.cw.service.config.nightaudit.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cw.cache.GlobalCache;
import com.cw.config.ScheduleWorkConfig;
import com.cw.config.exception.CustomException;
import com.cw.entity.NaConfig;
import com.cw.entity.NaDailyLog;
import com.cw.entity.NaParam;
import com.cw.entity.NaRunInfo;
import com.cw.mapper.NaConfigMapper;
import com.cw.mapper.NaDailyLogMapper;
import com.cw.mapper.NaParamMapper;
import com.cw.mapper.NaRunInfoMapper;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.pms.res.na.req.NaConfig_Req;
import com.cw.pojo.dto.pms.res.na.req.NaDailyLog_Req;
import com.cw.pojo.dto.pms.res.na.res.NaConfig_Res;
import com.cw.pojo.dto.pms.res.na.res.NaDailyLogDetail;
import com.cw.pojo.dto.pms.res.na.res.NaDailyLog_Res;
import com.cw.pojo.dto.pms.res.na.res.NaProgress_Res;
import com.cw.pojo.entity.NaConfigEntity;
import com.cw.pojo.entity.NaParamEntity;
import com.cw.pojo.entity.NaRunInfoEntity;
import com.cw.service.config.nightaudit.NightAuditService;
import com.cw.service.context.GlobalContext;
import com.cw.service.mq.MqNameUtils;
import com.cw.service.mq.ScheduleMsg;
import com.cw.service.na.tasks.TaskSequenceEnum;
import com.cw.service.scheduletask.job.CrsCronNaJob;
import com.cw.utils.CalculateDate;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;
import com.cw.utils.na.NaProgressStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.quartz.impl.JobDetailImpl;
import org.quartz.impl.triggers.CronTriggerImpl;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.*;

import static com.cw.service.scheduletask.ScheduleJobDispatcher.*;


/**
 * @Describe
 * <AUTHOR> Tony Leung
 * @Create on 2024-03-20
 */
@Slf4j
@Service
public class NightAuditServiceImpl implements NightAuditService {

    @Autowired
    NaConfigMapper naconfigMapper;
    @Autowired
    NaParamMapper naparamMapper;
    @Autowired
    NaRunInfoMapper naruninfoMapper;
    @Autowired
    NaDailyLogMapper naDailyLogMapper;

    @Autowired
    RabbitTemplate rabbitTemplate;


    @Override
    public NaConfig_Res queryParamList(NaConfig_Req req) {
        String hotelId = GlobalContext.getCurrentHotelId();
        int currentPage = req.getPages().getCurrentpage() < 1 ? 0 : req.getPages().getCurrentpage() - 1;
        int pageSize = req.getPages().getPagesize();
        Sort sort = Sort.by(req.getPages().getDirection(), req.getPages().getSortname().split(","));
        Page<NaParam> naParamPage = naparamMapper.findAll((Root<NaParam> root, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
            List<Predicate> list = new ArrayList<Predicate>();
            list.add(cb.equal(root.get(SystemUtil.hotelIdColumn), hotelId));
            Predicate[] p = new Predicate[list.size()];
            query.where(cb.and(list.toArray(p)));
            return query.getRestriction();
        }, PageRequest.of(currentPage, pageSize));
        naParamPage.getSort().and(sort);
        NaConfig_Res res = new NaConfig_Res();
        res.fillPageData(naParamPage);
        //获取夜审配置的夜审运行时间
        NaConfig config = naconfigMapper.findByHotelId(hotelId);
        String defaultRuntime = "01:30";
        if (config != null) {
            defaultRuntime = config.getRuntime();
        }
        res.setRuntime(defaultRuntime);
        return res;
    }

    @Override
    public NaConfigEntity saveConfig(String runtime) {
        String hotelId = GlobalContext.getCurrentHotelId();
        NaConfig naconfig = naconfigMapper.findByHotelId(hotelId);
        if (naconfig == null) {
            naconfig = new NaConfig();
            naconfig.setHotelId(hotelId);
        }
        //保留初始值做比较
        NaConfigEntity dbEntity = new NaConfigEntity();
        BeanUtil.copyProperties(naconfig, dbEntity);

        String orgtime = naconfig.getRuntime();
        String[] hourmin = runtime.split(":");
        String cronExp = String.format("0 %S %S * * ? ", hourmin[1], hourmin[0]);
        if (!CronExpression.isValidExpression(cronExp)) {
            throw new CustomException(ResultJson.failure(ResultCode.SERVER_ERROR)
                    .msg("错误的时间表达式:" + cronExp));
        }
        naconfig.setRuntime(runtime);
        naconfig.setCronTime(cronExp);
        naconfig = naconfigMapper.saveAndFlush(naconfig);
        //刷新任务信息
        refreshNaRunInfo(naconfig);

        if (!naconfig.getRuntime().equals(orgtime)) {
            //广播消息刷新
            notifyRefreshDailyTask(naconfig);
        }
        NaConfigEntity entity = new NaConfigEntity();
        BeanUtil.copyProperties(naconfig, entity);
        //保存操作日志
        return entity;
    }

    /**
     * @param naConfig
     */
    private void notifyRefreshDailyTask(NaConfig naConfig) {
        //线程名字和任务名字加上hotelId做区分
        String hotelId = naConfig.getHotelId().toUpperCase(Locale.ROOT);
        ScheduleMsg scheduleMsg = new ScheduleMsg(DSPMS_CRONTRIGGER_GROUP, DSPMS_CRONNATRIGGER_NAME + hotelId,
                DSPMS_CRONJOB_GROUP, DSPMS_CRONNAJOB_NAME + hotelId, SystemUtil.ScheduleOp.CRON_TRIGGER);
        scheduleMsg.setCronTime(naConfig.getCronTime());

        rabbitTemplate.convertAndSend(MqNameUtils.FanoutExchange.SCHEDULE.name(),
                "", JSON.toJSONString(scheduleMsg));
    }

    /**
     * 刷新对应配置下的夜审运行状态
     *
     * @param naConfig
     */
    private void refreshNaRunInfo(NaConfig naConfig) {
        NaRunInfo naRunInfo = naruninfoMapper.findTopByHotelIdOrderByIdDesc(naConfig.getHotelId());
        if (naRunInfo == null) {
            naRunInfo = new NaRunInfo();
            //明天凌晨,统计今天到的数据
            naRunInfo.setNaDate(CalculateDate.getSystemDate());
            naRunInfo.setStart(naConfig.getRuntime());
            naRunInfo.setNextProgress("1");
            naRunInfo.setRunNing("0");
            //下次运行在明天凌晨
            naRunInfo.setRunDate(CalculateDate.reckonDay(naRunInfo.getNaDate(), 5, 1));
        }
        naRunInfo.setStart(naConfig.getRuntime());
        naruninfoMapper.saveAndFlush(naRunInfo);
    }

    @Override
    public NaParam loadNaParam(Long id) {
        Optional<NaParam> optional = naparamMapper.findById(id);
        if (!optional.isPresent()) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("记录未找到"));
        }
        return optional.get();
    }

    @Override
    public NaParamEntity saveParams(NaParamEntity entity) {
        NaParam naParam = entity.getId() <= 0 ? new NaParam() : loadNaParam(entity.getId());
        NaParamEntity dbEntity = new NaParamEntity();
        //保存初始数据做对比
        BeanUtil.copyProperties(naParam, dbEntity);
        String hotelId = GlobalContext.getCurrentHotelId();
        //复制信息保存
        BeanUtil.copyProperties(entity, naParam, "id");
        naParam.setHotelId(hotelId);
        if (entity.getId() <= 0 && naparamMapper.countByParamName(entity.getParamName()) > 0) {
            throw new CustomException(ResultJson.failure(ResultCode.SERVER_ERROR)
                    .msg("参数名" + entity.getParamName() + "重复"));
        }
        naparamMapper.saveAndFlush(naParam);
        SpringUtil.getBean(GlobalCache.class).refreshAndNotify(GlobalDataType.NA_PARAM, hotelId);
        NaParamEntity res = new NaParamEntity();
        BeanUtil.copyProperties(naParam, res);
        return res;
    }

    @Override
    public void deleteNaParam(Long id) {
        NaParam naParam = loadNaParam(id);
        naparamMapper.deleteById(naParam.getId());

    }

    @Override
    public NaRunInfoEntity getNaRunInfo() {
        String hotelId = GlobalContext.getCurrentHotelId();
        NaRunInfoEntity entity = new NaRunInfoEntity();
        NaRunInfo naRunInfo = naruninfoMapper.findTopByHotelIdOrderByIdDesc(hotelId);
        if (naRunInfo != null) {
            BeanUtil.copyProperties(naRunInfo, entity);
        }
        NaConfig naConfig = naconfigMapper.findByHotelId(hotelId);
        String defaultTime = "00:30:00";
        if (naConfig != null) {
            defaultTime = naConfig.getRuntime() + ":00";
        }
        //下次执行就在第二天的夜审时间
        entity.setNextStart(CalculateDate.dateToString(CalculateDate.reckonDay(new Date(), 5, 1)) + " " + defaultTime);
        return entity;
    }

    /**
     * 获取夜审信息集合 包括步骤详情
     *
     * @param hotelId
     * @return
     */
    @Override
    public NaProgress_Res getNaRunInfoAndProgress(String hotelId) {
        NaProgress_Res res = new NaProgress_Res();
        List<NaDailyLogDetail> detailList = new ArrayList<>();
        boolean lsuccess = true;
        NaRunInfo naRunInfo = naruninfoMapper.findTopByHotelIdOrderByIdDesc(hotelId);
        if (naRunInfo == null) {
            throw new CustomException(ResultJson.failure(ResultCode.NOT_FOUND).msg("未配置夜审，请检查"));
        }
        BeanUtil.copyProperties(naRunInfo, res);
        //如果正在运行则不显示结束时间
        if (naRunInfo.getRunNing().equals("1")) {
            res.setLastEnd(null);
        }
        //先默认初始化显示
        TaskSequenceEnum[] values = TaskSequenceEnum.values();
        for (TaskSequenceEnum node : values) {
            Integer seq = node.getSeq();
            NaDailyLogDetail detail = new NaDailyLogDetail();
            detail.setProgressId(seq + "");
            String progressName = detail.getProgressId() + "." + node.getDesc();
            detail.setProgressName(progressName);
            detail.setColor(NaProgressStatusEnum.INIT.getColor());
            detail.setStatus(NaProgressStatusEnum.INIT.getDesc());
            detail.setMsg("等待任务开始");

            detailList.add(detail);
        }

        Date naDate = DateUtil.beginOfDay(naRunInfo.getNaDate());
        Date maxDate = naDailyLogMapper.getMaxNaDailyLogNaDate(hotelId);
        if (maxDate != null) {
            //两个日期不等 说明夜审失败，得取实际夜审步骤最后时间
            if (naDate.compareTo(maxDate) != 0) {
                lsuccess = false;
            }
            //可能夜审报错  取最大日期记录
            naDate = CalculateDate.maxDate(naDate, maxDate);
        }

        //List<NaDailyLog> dailyLogList = naDailyLogMapper.getNaDailyLog(hotelId, naDate);
        //可能中途启动 删除了之前的记录 没有步骤1的记录
        //if (CollectionUtil.isNotEmpty(dailyLogList)) {
        //    NaDailyLog currentDailyLog = dailyLogList.get(0);//获取最新步骤1
        //    //获取当前最新步骤1的ID起始相同runip的夜审记录  保证和步骤数量一致
        //    List<NaDailyLog> currentDailyLogList = naDailyLogMapper.getLastNaDailyLogs(hotelId, naDate, currentDailyLog.getId());
        //    String lastProgressId = currentDailyLogList.get(currentDailyLogList.size() - 1).getProgressId();
        //    Integer currentProgress = Integer.parseInt(lastProgressId);//获取任务步骤
        //    //计算当前进度百分比   TaskSequenceEnum 序号对比
        //    for (TaskSequenceEnum node : values) {
        //        Integer seq = node.getSeq();
        //        NaDailyLogDetail detail = new NaDailyLogDetail();
        //        detail.setProgressId(seq + "");
        //        String progressName = detail.getProgressId() + "." + node.getDesc();
        //        detail.setProgressName(progressName);
        //        int index = seq - 1;
        //        if (node.getSeq() <= currentProgress) {//存在说明当前步骤已经完成
        //            NaDailyLog currentLog = currentDailyLogList.get(index);
        //            if (SystemUtil.NASUCCESS.equals(currentLog.getStatus())) {
        //                detail.setStatus(NaProgressStatusEnum.FINISH.getDesc());
        //                detail.setColor(NaProgressStatusEnum.FINISH.getColor());
        //                String msg = StrUtil.format("完成时间:{}", currentLog.getEndTime());
        //                detail.setMsg(msg);
        //                //如果是应到未到和应离未离 直接显示msg信息
        //                String taskSeq = TaskSequenceEnum.RESOURCE_DAILY_TASK.getSeq() + "";
        //                if (taskSeq.equals(currentLog.getProgressId()) && !"完成".equals(currentLog.getMsg())) {
        //                    detail.setMsg(currentLog.getMsg());
        //                }
        //                detailList.add(detail);
        //            } else {
        //                //失败则报错
        //                detail.setStatus(NaProgressStatusEnum.ERROR.getDesc());
        //                detail.setColor(NaProgressStatusEnum.ERROR.getColor());
        //                detail.setMsg(currentLog.getMsg());
        //                detailList.add(detail);
        //            }
        //
        //        } else {
        //            //任务步骤节点大于当前任务步骤，默认未开始 如果是大于1 则可表示进行中
        //            if (node.getSeq() - currentProgress == 1) {
        //                detail.setMsg(NaProgressStatusEnum.UNDERWAY.getDesc());
        //                detail.setColor(NaProgressStatusEnum.UNDERWAY.getColor());
        //                //获取最后一个元素
        //                NaDailyLog lastProgressLog = currentDailyLogList.get(currentDailyLogList.size() - 1);
        //                Date endTime = DateUtil.parse(lastProgressLog.getEndTime(), "yyyy-MM-dd HH:mm:ss");
        //                //上一步骤结束时间多加1秒
        //                Date nextStarTime = DateUtil.offset(endTime, DateField.SECOND, 1);
        //                String msg = StrUtil.format("开始时间:{}", DateUtil.format(nextStarTime, "yyyy-MM-dd HH:mm:ss"));
        //                detail.setMsg(msg);
        //            }
        //            //其余则是按正常显示
        //            detailList.add(detail);
        //        }
        //    }
        //}
        List<NaDailyLog> dailyLogList = naDailyLogMapper.getMaxNaDailyLog(hotelId, naDate);
        if (CollectionUtil.isNotEmpty(dailyLogList)) {
            //默认初始化步骤填充夜审最新步骤信息 取第一条匹配记录
            for (NaDailyLogDetail detailNode : detailList) {
                for (NaDailyLog node : dailyLogList) {
                    if (detailNode.getProgressId().equals(node.getProgressId())) {
                        if (SystemUtil.NASUCCESS.equals(node.getStatus())) {
                            detailNode.setStatus(NaProgressStatusEnum.FINISH.getDesc());
                            detailNode.setColor(NaProgressStatusEnum.FINISH.getColor());
                            String msg = StrUtil.format("完成时间:{}", node.getEndTime());
                            detailNode.setMsg(msg);
                            //如果是应到未到和应离未离 直接显示msg信息
                            String taskSeq = TaskSequenceEnum.RESOURCE_DAILY_TASK.getSeq() + "";
                            if (taskSeq.equals(node.getProgressId()) && !"完成".equals(node.getMsg())) {
                                detailNode.setMsg(node.getMsg());
                            }
                        } else {
                            //失败则报错
                            detailNode.setStatus(NaProgressStatusEnum.ERROR.getDesc());
                            detailNode.setColor(NaProgressStatusEnum.ERROR.getColor());
                            detailNode.setMsg(node.getMsg());
                        }
                        break;
                    }
                }

            }

        }
        if (!lsuccess) {
            //显示最后步骤的开始时间和结束时间
            NaDailyLog last = dailyLogList.stream().max(Comparator.comparing(NaDailyLog::getProgressId)).orElse(null);
            if (last != null) {
               res.setLastEnd(last.getEndTime());
            }


        }

        res.setDetails(detailList);

        //步骤进度百分比
        double progressRate = (double) Integer.parseInt(naRunInfo.getRunProgress()) * 100 / values.length;
        res.setProgressRate(Double.toString(progressRate).split("\\.")[0]);//取整数部分

        return res;
    }

    /**
     * 重跑夜审.先暂停夜审任务.重新跑一次.
     */
    @Override
    public void resetDailyTask() {
        String hotelId = GlobalContext.getCurrentHotelId();
        NaRunInfo naRunInfo = naruninfoMapper.findTopByHotelIdOrderByIdDesc(hotelId);
        NaConfig naConfig = naconfigMapper.findByHotelId(hotelId);
        if (naConfig == null || naRunInfo == null) {
            throw new CustomException(ResultJson.failure(ResultCode.SERVER_ERROR).msg("夜审未配置"));
        }
        try {
            String triggerName = DSPMS_CRONNATRIGGER_NAME + hotelId;
            String triggerGroup = DSPMS_CRONTRIGGER_GROUP;
            Scheduler scheduler = SpringUtil.getBean(ScheduleWorkConfig.quartzSchedulerName, Scheduler.class);
            TriggerKey key = TriggerKey.triggerKey(triggerName, triggerGroup);
            Trigger trigger = scheduler.getTrigger(key);

            // 如果定时任务不存在，先创建它
            if (trigger == null) {
                log.warn("酒店{}的夜审定时任务不存在，正在创建...", hotelId);
                startupNaThread(naConfig);
                trigger = scheduler.getTrigger(key);
            }

            if (trigger == null) {
                throw new CustomException(ResultJson.failure(ResultCode.SERVER_ERROR).msg("服务线程不存在"));
            }
            Trigger.TriggerState triggerState = scheduler.getTriggerState(trigger.getKey());

            Date sysdate = CalculateDate.getSystemDate();
            Date yesterday = CalculateDate.reckonDay(sysdate, 5, -1);

            naRunInfo.setNaDate(yesterday);  //表示昨天的已经完成
            naRunInfo.setRunDate(sysdate);//标识今天已经运行.
            naruninfoMapper.saveAndFlush(naRunInfo);

            JobDataMap dataMap = new JobDataMap();
            dataMap.put("RESET", true); //增加参数

            scheduler.triggerJob(trigger.getJobKey(), dataMap);
        } catch (SchedulerException e) {
            e.printStackTrace();
        }
    }

    @Override
    public NaDailyLog_Res queryNaLog(NaDailyLog_Req req) {
        int currentPage = req.getPages().getCurrentpage() < 1 ? 0 : req.getPages().getCurrentpage() - 1;
        int pageSize = req.getPages().getPagesize();
        req.getPages().setSortorder("descending");
        req.getPages().setSortname("id");
        String queryDateStr = req.getQueryDate();
        Page<NaDailyLog> naDailyLogPage = naDailyLogMapper.findAll((Root<NaDailyLog> root, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
            List<Predicate> list = new ArrayList<Predicate>();
            if (queryDateStr != null && !queryDateStr.isEmpty()) {
                Date queryDate = CalculateDate.stringToDate(queryDateStr);
                list.add(cb.equal(root.get("naDate"), queryDate));
            }
            list.add(cb.equal(root.get(SystemUtil.hotelIdColumn), GlobalContext.getCurrentHotelId()));
            Predicate[] p = new Predicate[list.size()];
            query.where(cb.and(list.toArray(p)));
            return query.getRestriction();
        }, PageRequest.of(currentPage, pageSize, Sort.by(req.getPages().getDirection(),
                req.getPages().getSortname().split(","))));
        NaDailyLog_Res res = new NaDailyLog_Res();
        res.fillPageData(naDailyLogPage);
        return res;
    }

    public void startupNaThread(NaConfig naconfig) {
        try {
            Scheduler scheduler = SpringUtil.getBean(ScheduleWorkConfig.quartzSchedulerName, Scheduler.class);
            //修改 hoetlid取原来
            String hotelId = naconfig.getHotelId();
            JobDetailImpl job = new JobDetailImpl();
            job.setGroup(DSPMS_CRONJOB_GROUP);
            job.setName(DSPMS_CRONNAJOB_NAME + hotelId);
            job.setJobClass(CrsCronNaJob.class);
            job.getJobDataMap().put("hotelId", hotelId);

            CronTriggerImpl cronTrigger = new CronTriggerImpl();
            cronTrigger.setName(DSPMS_CRONNATRIGGER_NAME + hotelId);
            cronTrigger.setGroup(DSPMS_CRONTRIGGER_GROUP);
            cronTrigger.setDescription("夜审线程" + hotelId);

            //每日都定时释放
            if (naconfig.getCronTime().isEmpty()) {
                //每日晚上12点开始执行  0 30 0 * * ?  多个线程任务时是否错开呢??
                cronTrigger.setCronExpression("0 30 0 * * ?");
            } else {   //按设定的计划日期释放s
                String cronExp = naconfig.getCronTime();
                if (!CronExpression.isValidExpression(cronExp)) {
                    throw new Exception("错误的日期表达式: " + cronExp);
                }
                cronTrigger.setCronExpression(cronExp);
            }
            scheduler.scheduleJob(job, cronTrigger);

            log.info("夜审将在{} 进行.线程已经启动", naconfig.getRuntime());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
