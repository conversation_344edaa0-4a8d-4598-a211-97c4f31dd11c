package com.cw.service.config.common;

import com.cw.pojo.dto.common.pagedata.PaymentSelectDataNode;
import com.cw.pojo.dto.common.pagedata.RmtSelectDataNode;
import com.cw.pojo.dto.common.pagedata.SelectDataNode;
import com.cw.pojo.dto.common.req.CommonSelectReq;
import com.cw.pojo.dto.common.res.Common_Select_Res;

import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/9/23 0023
 */
public interface CustomDataService {
    List<Common_Select_Res> getSelectData(CommonSelectReq req, String hotelId);

    List<PaymentSelectDataNode> getPaymentSelectData(CommonSelectReq req, String hotelId);

    List<SelectDataNode> getRoomSelectData(CommonSelectReq req, String hotelId);

    List<RmtSelectDataNode> getRoomTypeSelectData(CommonSelectReq req, String hotelId);

    /**
     * 获取按楼号分组的房型选择数据
     * @param req 请求参数
     * @param hotelId 酒店ID
     * @return 按楼号分组的房型数据，包含ALL选项和各楼号的房型列表
     */
    Common_Select_Res getRoomTypeByBuildingSelectData(CommonSelectReq req, String hotelId);

}
