package com.cw.pojo.dto.pms.req.pkgdet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 包价详情请求实体
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
@Data
@ApiModel(description = "包价详情信息请求对象")
public class PkgdetReq implements Serializable {

    /**
     * 房价代码
     */
    @ApiModelProperty(value = "房价代码", required = true, example = "PKG001")
    @NotBlank(message = "房价代码不允许为空")
    private String ratecode;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间 yyyy-MM-dd", required = true, example = "2024-08-01")
    @NotNull(message = "开始时间不能为空")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间 yyyy-MM-dd", required = true, example = "2024-08-31")
    @NotNull(message = "结束时间不能为空")
    private Date endtime;

    /**
     * 房型代码列表（支持多个房型）
     */
    @ApiModelProperty(value = "房型代码列表", required = true, example = "[\"STD\", \"SUP\", \"DLX\"]")
    @NotNull(message = "房型代码列表不能为空")
    private List<String> roomtypes;

    /**
     * 包价代码
     */
    @ApiModelProperty(value = "包价代码，多个以逗号分隔", required = true, example = "3|BRF,1|WC,2|TK")
    @NotBlank(message = "包价代码不允许为空")
    private String includeCode;
}
