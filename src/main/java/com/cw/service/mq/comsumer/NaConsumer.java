package com.cw.service.mq.comsumer;

import com.alibaba.fastjson.JSON;
import com.cw.entity.NaDailyLog;
import com.cw.entity.NaRunInfo;
import com.cw.service.mq.MqNameUtils;
import com.cw.service.na.NaService;
import com.cw.service.na.tasks.NaTaskInfo;
import com.cw.service.na.tasks.TaskFactory;
import com.cw.service.na.tasks.impl.BaseDailyTask;
import com.cw.utils.CalculateDate;
import com.cw.utils.SpringUtil;
import com.google.common.collect.Maps;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-03-27
 */
@Component
public class NaConsumer implements ChannelAwareMessageListener {

    @Autowired
    RabbitTemplate rabbitTemplate;

    @Autowired
    private Queue configNaQueue;
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public void onMessage(Message message, Channel channel)  {
        String msg = new String(message.getBody());
        String routingKey = message.getMessageProperties().getReceivedRoutingKey();
        try {
            //判断发送消息路由和本机相同才进行夜审，否则直接消费
            if (!routingKey.equals(configNaQueue.getName())){
                if (channel.isOpen()) {
                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                }
                return;
            }

            NaRunInfo runInfo = JSON.parseObject(msg, NaRunInfo.class);
            if (runInfo == null || "0".equals(runInfo.getRunNing())) {
                logger.info("夜审已结束");
                if (channel.isOpen()) {
                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                }
                return;
            }
            if ("STD".equals(runInfo.getRunNing())) {
                logger.info("酒店STD夜审跳过");
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            if (runInfo.getId() <= 0L) {
                //其他路由发送的消息 直接返回
                if (channel.isOpen()) {
                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                }
                return;
            }
            Integer progress = Integer.parseInt(runInfo.getNextProgress());
            BaseDailyTask task = TaskFactory.getBaseDailyTask(progress);
            if (task != null) {
                //避免超出当前日期
                Date newNaDate = CalculateDate.minDate(CalculateDate.reckonDay(new Date(), 5, -1),
                        CalculateDate.reckonDay(runInfo.getNaDate(), 5, 1));
                //按日期取步骤
                NaDailyLog nadailylog = new NaDailyLog();
                nadailylog.setNaDate(CalculateDate.returnDate_ZeroTime(newNaDate));
                nadailylog.setHotelId(runInfo.getHotelId());
                nadailylog.setProgressId(progress + "");
                task.setProcessId(progress);
                NaTaskInfo taskInfo = new NaTaskInfo(nadailylog, Maps.newHashMap());
                task.setNaTaskInfo(taskInfo);
                NaService naService = SpringUtil.getBean(NaService.class);
                task.setNaService(naService);
            }
            boolean isDone = (task != null && task.runTask(runInfo));
            //如果任务执行成功，判断是否是最后执行步骤，不是最后步骤则执行序号，然后发送消息
            if (isDone) {
                if (channel.isOpen()) {
                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                }
                if (!TaskFactory.isTaskEnd(task.getProcessId())) {
                    int nextProcessId = task.getProcessId();
                    nextProcessId = nextProcessId + 1;
                    runInfo.setNextProgress(nextProcessId + "");

                    rabbitTemplate.convertAndSend(MqNameUtils.FanoutExchange.NA.name(),
                            routingKey, JSON.toJSONString(runInfo));

                } else {
                    //hotelId夜审结束，明晚从第一步开始
                    runInfo.setNextProgress("1");
                    runInfo.setLastStart(runInfo.getStart());
                    runInfo.setLastEnd(CalculateDate.spd_time.format(System.currentTimeMillis()));
                    //标记为跑完了.没在跑
                    runInfo.setRunNing("0");
                    task.getNaService().finishWork(runInfo);
                    logger.info("★★★★★项目{}本次夜审已经完成★★★★★", runInfo.getHotelId());

                }
                //运行成功消费消息
                return;
            } else {
                //抛出异常 直接消费消息  等重设夜审消息  否则会 接受不到后续消息
                if (channel.isOpen()) {
                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                }
            }
        }catch (RuntimeException e){
            System.out.println("naconsummer  RuntimeException异常");
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("naconsummer Exception异常");
        } finally {

        }
    }


    //
    ///**
    // * 新管道发送消息 避免读写堵塞关闭管道
    // *
    // * @param info
    // * @throws IOException
    // * @throws TimeoutException
    // */
    //private void sendNewMessage(Message message, String info) throws IOException, TimeoutException {
    //    // 创建一个新的管道来发送消息,避免在同一个管道上进行读写操作
    //    Channel newChannel = rabbitTemplate.getConnectionFactory().createConnection().createChannel(false);
    //    try {
    //        String exchange = message.getMessageProperties().getReceivedExchange();
    //        String routingKey = message.getMessageProperties().getReceivedRoutingKey();
    //        logger.info("exchange:{}\n routingKey:{}",exchange,routingKey);
    //        AMQP.BasicProperties properties = new AMQP.BasicProperties.Builder()
    //                .headers(message.getMessageProperties().getHeaders())
    //                .build();
    //        // 在新的管道上发送消息
    //        newChannel.basicPublish(exchange, routingKey, properties, info.getBytes());
    //    } catch (IOException e) {
    //        e.printStackTrace();
    //    } finally {
    //        // 关闭新创建的管道
    //        newChannel.close();
    //    }
    //}
}
