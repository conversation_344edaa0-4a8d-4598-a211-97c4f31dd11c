package com.cw.service.config.common.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.cw.cache.CustomData;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.PaymentCache;
import com.cw.entity.Building;
import com.cw.entity.Payment;
import com.cw.entity.Room;
import com.cw.entity.RoomType;
import com.cw.pojo.dto.common.pagedata.PaymentSelectDataNode;
import com.cw.pojo.dto.common.pagedata.RmtSelectDataNode;
import com.cw.pojo.dto.common.pagedata.SelectDataNode;
import com.cw.pojo.dto.common.req.CommonSelectParam;
import com.cw.pojo.dto.common.req.CommonSelectReq;
import com.cw.pojo.dto.common.res.Common_Select_Res;
import com.cw.service.config.common.CustomDataService;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.AccountItemEnum;
import com.cw.utils.enums.GlobalDataType;
import com.cw.utils.enums.pay.PaymentSelectOption;
import io.jsonwebtoken.lang.Collections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> Tony Leung
 * @Create on 2021/9/23 0023
 */
@Service
public class CustomDataServiceImpl implements CustomDataService {

    @Autowired
    CustomData customDataFactory;


    /**
     * 获取下拉框数据
     *
     * @param req       "tickettype,hoteltype,kittheme..."
     * @param hotelId 项目id
     * @return {"ticketType":["code":"codeValue","desc":"descValue"],"hotelType":["code":"codeValue","desc":"descValue"]}
     */
    @Override
    public List<Common_Select_Res> getSelectData(CommonSelectReq req, String hotelId) {
        List<Common_Select_Res> list = new ArrayList<>();
        Common_Select_Res res;
        List<CommonSelectParam> params = req.getParams();
        String[] keyNames = req.getKeyName().split(",");
        for (String key : keyNames) {
            res = new Common_Select_Res();
            res.setKeyname(key);
            res.setValues(customDataFactory.getSelectData(hotelId, SystemUtil.CustomDataKey.valueOf(key.toLowerCase(Locale.ROOT)), getParam(params, key)));
            list.add(res);
        }
        return list;
    }

    @Override
    public List<PaymentSelectDataNode> getPaymentSelectData(CommonSelectReq req, String hotelId) {
        List<PaymentSelectDataNode> selectDataNodes = new ArrayList<>();
        PaymentCache paymentCache = GlobalCache.getDataStructure().getCache(GlobalDataType.PAYMENT);
        List<Payment> paymentList = paymentCache.getDataList(hotelId);
        Set<Integer> optionSet = req.getOptions().stream().collect(Collectors.toSet());//过两天再回来补充. 有些场景需要排除.比如非AR
        for (Payment payment : paymentList) {
            if (CollectionUtil.isNotEmpty(optionSet)) {
                if (PaymentSelectOption.shouldExcludePayment(payment, optionSet)) { // 按照排除, 排除掉不需要的支付方式 .
                    continue;
                }
            }
            PaymentSelectDataNode selectDataNode = new PaymentSelectDataNode();
            selectDataNode.setCode(payment.getPayCode());
            selectDataNode.setDesc(payment.getDescription());
            selectDataNode.setLifc(AccountItemEnum.isSysOnlinePay(payment.getDepartmentCode()));//是否接口付款
            selectDataNode.setLar(AccountItemEnum.isArPay(payment.getDepartmentCode())); //是否挂账
            selectDataNode.setGroup(payment.getDepartmentCode());
            selectDataNodes.add(selectDataNode);
        }
        return selectDataNodes;
    }

    /**
     * 获取房间下拉数据
     *
     * @param req
     * @param hotelId
     * @return
     */
    @Override
    public List<SelectDataNode> getRoomSelectData(CommonSelectReq req, String hotelId) {
        List<Room> roomList = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOM).getDataList(hotelId);
        Map<String, List<Room>> roomGroups = roomList.stream().collect(Collectors.groupingBy(Room::getRoomType));
        List<RoomType> roomTypeList = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOMTYPE).getDataList(hotelId);
        List<SelectDataNode> selectDataNodes = new ArrayList<>();
        for (RoomType roomType : roomTypeList) {
            if (!roomType.getGeneric()) {
                SelectDataNode selectDataNode = new SelectDataNode();
                selectDataNode.setCode(roomType.getRoomType());
                selectDataNode.setDesc(roomType.getDescription());
                if (roomGroups.containsKey(roomType.getRoomType())) {
                    selectDataNode.setChildren(roomGroups.get(roomType.getRoomType()).stream().map(r -> new SelectDataNode(r.getRoomNo(), r.getRoomNo())).collect(Collectors.toList()));
                } else {
                    selectDataNode.setChildren(Collections.emptyList());
                }
                selectDataNodes.add(selectDataNode);
            }
        }
        return selectDataNodes;
    }

    private String getParam(List<CommonSelectParam> params, String key) {
        if (params != null && !params.isEmpty()) {
            for (CommonSelectParam param : params) {
                if (param.getKey().equals(key)) {
                    return param.getValue();
                }
            }
        }
        return "";
    }

    @Override
    public List<RmtSelectDataNode> getRoomTypeSelectData(CommonSelectReq req, String hotelId) {
        List<RoomType> roomTypeList = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOMTYPE).getDataList(hotelId);
        List<RmtSelectDataNode> selectDataNodes = new ArrayList<>();
        for (RoomType roomType : roomTypeList) {
            if (!roomType.getGeneric()) {
                RmtSelectDataNode selectDataNode = new RmtSelectDataNode();
                selectDataNode.setCode(roomType.getRoomType());
                selectDataNode.setDesc(roomType.getDescription());
                selectDataNode.setGeneric(roomType.getGeneric());
                selectDataNodes.add(selectDataNode);
            }
        }
        return selectDataNodes;
    }

    @Override
    public List<SelectDataNode> getRoomTypeByBuildingSelectData(CommonSelectReq req, String hotelId) {
        // 获取所有房型数据
        List<RoomType> roomTypeList = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOMTYPE).getDataList(hotelId);
        // 获取所有楼栋数据
        List<Building> buildingList = GlobalCache.getDataStructure().getCache(GlobalDataType.BUILDING).getDataList(hotelId);

        List<SelectDataNode> result = new ArrayList<>();

        // 创建ALL选项，包含所有房型
        SelectDataNode allNode = new SelectDataNode();
        allNode.setCode("ALL");
        allNode.setDesc("所有房型");
        allNode.setChildren(new ArrayList<>());

        // 添加所有房型到ALL节点
        for (RoomType roomType : roomTypeList) {
            if (!roomType.getGeneric()) { // 排除套房
                SelectDataNode roomTypeNode = new SelectDataNode();
                roomTypeNode.setCode(roomType.getRoomType());
                roomTypeNode.setDesc(roomType.getDescription());
                allNode.getChildren().add(roomTypeNode);
            }
        }
        result.add(allNode);

        // 按楼栋分组房型
        Map<String, List<RoomType>> roomTypesByBuilding = roomTypeList.stream()
                .filter(rt -> !rt.getGeneric() && StrUtil.isNotBlank(rt.getBuildingNo())) // 排除套房和空楼号
                .collect(Collectors.groupingBy(RoomType::getBuildingNo));

        // 为每个楼栋创建节点
        for (Building building : buildingList) {
            if (roomTypesByBuilding.containsKey(building.getCode())) {
                SelectDataNode buildingNode = new SelectDataNode();
                buildingNode.setCode(building.getCode());
                buildingNode.setDesc(building.getDescription());
                buildingNode.setChildren(new ArrayList<>());

                // 添加该楼栋的房型
                List<RoomType> buildingRoomTypes = roomTypesByBuilding.get(building.getCode());
                for (RoomType roomType : buildingRoomTypes) {
                    SelectDataNode roomTypeNode = new SelectDataNode();
                    roomTypeNode.setCode(roomType.getRoomType());
                    roomTypeNode.setDesc(roomType.getDescription());
                    buildingNode.getChildren().add(roomTypeNode);
                }

                result.add(buildingNode);
            }
        }

        return result;
    }

}
