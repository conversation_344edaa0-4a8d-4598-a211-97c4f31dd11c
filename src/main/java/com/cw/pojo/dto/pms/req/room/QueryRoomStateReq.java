package com.cw.pojo.dto.pms.req.room;

import com.cw.pojo.dto.common.req.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @Description: 查询房间
 * @Author: michael.pan
 * @Date: 2024/5/25 13:52
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("查询房态请求对象")
public class QueryRoomStateReq extends PageReq implements Serializable {
    /**
     * 房间号
     */
    @ApiModelProperty(value = "房间号", example = "1001")
    private String roomNo;
    /**
     * 房间状态（DI,CL,OO）
     */
    @ApiModelProperty(value = "房间状态（DI,CL,OO）", example = "CL")
    private String roomStatus;

    @ApiModelProperty(value = "占用（查询占用房态时locc必须为1）", example = "1")
    private int locc;

    @ApiModelProperty(value = "房型", example = "GJDCF")
    private String roomType;

    @ApiModelProperty(value = "楼号", example = "101")
    private String buildingNo;

    @ApiModelProperty(value = "关联今日预订状态查询", example = "1-今日入住，2-今日预离")
    private Integer rsTodayType;
}
