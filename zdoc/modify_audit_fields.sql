-- =====================================================
-- 修改继承自BaseEntity的表的审计字段为可空
-- 说明: 将create_time, modified_time, create_by, modified_by字段改为允许NULL
-- =====================================================

-- 1. account_item 表
ALTER TABLE `account_item`
    MODIFY COLUMN `create_time` datetime NULL COMMENT '创建时间',
    MODIFY COLUMN `modified_time` datetime NULL COMMENT '修改时间',
    MODIFY COLUMN `create_by` varchar (30) NULL COMMENT '创建人',
    MODIFY COLUMN `modified_by` varchar (30) NULL COMMENT '修改人';

-- 2. ar_account 表
ALTER TABLE `ar_account`
    MODIFY COLUMN `create_time` datetime NULL COMMENT '创建时间',
    MODIFY COLUMN `modified_time` datetime NULL COMMENT '修改时间',
    MODIFY COLUMN `create_by` varchar (30) NULL COMMENT '创建人',
    MODIFY COLUMN `modified_by` varchar (30) NULL COMMENT '修改人';

-- 3. brand 表
ALTER TABLE `brand`
    MODIFY COLUMN `create_time` datetime NULL COMMENT '创建时间',
    MODIFY COLUMN `modified_time` datetime NULL COMMENT '修改时间',
    MODIFY COLUMN `create_by` varchar (30) NULL COMMENT '创建人',
    MODIFY COLUMN `modified_by` varchar (30) NULL COMMENT '修改人';

-- 4. building 表
ALTER TABLE `building`
    MODIFY COLUMN `create_time` datetime NULL COMMENT '创建时间',
    MODIFY COLUMN `modified_time` datetime NULL COMMENT '修改时间',
    MODIFY COLUMN `create_by` varchar (30) NULL COMMENT '创建人',
    MODIFY COLUMN `modified_by` varchar (30) NULL COMMENT '修改人';

-- 5. channel 表
ALTER TABLE `channel`
    MODIFY COLUMN `create_time` datetime NULL COMMENT '创建时间',
    MODIFY COLUMN `modified_time` datetime NULL COMMENT '修改时间',
    MODIFY COLUMN `create_by` varchar (30) NULL COMMENT '创建人',
    MODIFY COLUMN `modified_by` varchar (30) NULL COMMENT '修改人';

-- 6. characteristic 表
ALTER TABLE `characteristic`
    MODIFY COLUMN `create_time` datetime NULL COMMENT '创建时间',
    MODIFY COLUMN `modified_time` datetime NULL COMMENT '修改时间',
    MODIFY COLUMN `create_by` varchar (30) NULL COMMENT '创建人',
    MODIFY COLUMN `modified_by` varchar (30) NULL COMMENT '修改人';

-- 7. group_band 表
ALTER TABLE `group_band`
    MODIFY COLUMN `create_time` datetime NULL COMMENT '创建时间',
    MODIFY COLUMN `modified_time` datetime NULL COMMENT '修改时间',
    MODIFY COLUMN `create_by` varchar (30) NULL COMMENT '创建人',
    MODIFY COLUMN `modified_by` varchar (30) NULL COMMENT '修改人';

-- 8. guest_accounts 表
ALTER TABLE `guest_accounts`
    MODIFY COLUMN `create_time` datetime NULL COMMENT '创建时间',
    MODIFY COLUMN `modified_time` datetime NULL COMMENT '修改时间',
    MODIFY COLUMN `create_by` varchar (30) NULL COMMENT '创建人',
    MODIFY COLUMN `modified_by` varchar (30) NULL COMMENT '修改人';

-- 9. guest_accounts_his 表
ALTER TABLE `guest_accounts_his`
    MODIFY COLUMN `create_time` datetime NULL COMMENT '创建时间',
    MODIFY COLUMN `modified_time` datetime NULL COMMENT '修改时间',
    MODIFY COLUMN `create_by` varchar (30) NULL COMMENT '创建人',
    MODIFY COLUMN `modified_by` varchar (30) NULL COMMENT '修改人';

-- 10. hotel 表
ALTER TABLE `hotel`
    MODIFY COLUMN `create_time` datetime NULL COMMENT '创建时间',
    MODIFY COLUMN `modified_time` datetime NULL COMMENT '修改时间',
    MODIFY COLUMN `create_by` varchar (30) NULL COMMENT '创建人',
    MODIFY COLUMN `modified_by` varchar (30) NULL COMMENT '修改人';

-- 11. include_rate 表
ALTER TABLE `include_rate`
    MODIFY COLUMN `create_time` datetime NULL COMMENT '创建时间',
    MODIFY COLUMN `modified_time` datetime NULL COMMENT '修改时间',
    MODIFY COLUMN `create_by` varchar (30) NULL COMMENT '创建人',
    MODIFY COLUMN `modified_by` varchar (30) NULL COMMENT '修改人';

-- 12. Invoice_Management 表
ALTER TABLE `Invoice_Management`
    MODIFY COLUMN `create_time` datetime NULL COMMENT '创建时间',
    MODIFY COLUMN `modified_time` datetime NULL COMMENT '修改时间',
    MODIFY COLUMN `create_by` varchar (30) NULL COMMENT '创建人',
    MODIFY COLUMN `modified_by` varchar (30) NULL COMMENT '修改人';

-- 13. market 表
ALTER TABLE `market`
    MODIFY COLUMN `create_time` datetime NULL COMMENT '创建时间',
    MODIFY COLUMN `modified_time` datetime NULL COMMENT '修改时间',
    MODIFY COLUMN `create_by` varchar (30) NULL COMMENT '创建人',
    MODIFY COLUMN `modified_by` varchar (30) NULL COMMENT '修改人';

-- 14. payment 表
ALTER TABLE `payment`
    MODIFY COLUMN `create_time` datetime NULL COMMENT '创建时间',
    MODIFY COLUMN `modified_time` datetime NULL COMMENT '修改时间',
    MODIFY COLUMN `create_by` varchar (30) NULL COMMENT '创建人',
    MODIFY COLUMN `modified_by` varchar (30) NULL COMMENT '修改人';


-- 16. profile 表
ALTER TABLE `profile`
    MODIFY COLUMN `create_time` datetime NULL COMMENT '创建时间',
    MODIFY COLUMN `modified_time` datetime NULL COMMENT '修改时间',
    MODIFY COLUMN `create_by` varchar (30) NULL COMMENT '创建人',
    MODIFY COLUMN `modified_by` varchar (30) NULL COMMENT '修改人';

-- 17. reservation 表
ALTER TABLE `reservation`
    MODIFY COLUMN `create_time` datetime NULL COMMENT '创建时间',
    MODIFY COLUMN `modified_time` datetime NULL COMMENT '修改时间',
    MODIFY COLUMN `create_by` varchar (30) NULL COMMENT '创建人',
    MODIFY COLUMN `modified_by` varchar (30) NULL COMMENT '修改人';

-- 18. reservation_his 表
ALTER TABLE `reservation_his`
    MODIFY COLUMN `create_time` datetime NULL COMMENT '创建时间',
    MODIFY COLUMN `modified_time` datetime NULL COMMENT '修改时间',
    MODIFY COLUMN `create_by` varchar (30) NULL COMMENT '创建人',
    MODIFY COLUMN `modified_by` varchar (30) NULL COMMENT '修改人';

-- 19. reservation_type 表
ALTER TABLE `reservation_type`
    MODIFY COLUMN `create_time` datetime NULL COMMENT '创建时间',
    MODIFY COLUMN `modified_time` datetime NULL COMMENT '修改时间',
    MODIFY COLUMN `create_by` varchar (30) NULL COMMENT '创建人',
    MODIFY COLUMN `modified_by` varchar (30) NULL COMMENT '修改人';

-- 20. room 表
ALTER TABLE `room`
    MODIFY COLUMN `create_time` datetime NULL COMMENT '创建时间',
    MODIFY COLUMN `modified_time` datetime NULL COMMENT '修改时间',
    MODIFY COLUMN `create_by` varchar (30) NULL COMMENT '创建人',
    MODIFY COLUMN `modified_by` varchar (30) NULL COMMENT '修改人';

-- 21. room_rate 表
ALTER TABLE `room_rate`
    MODIFY COLUMN `create_time` datetime NULL COMMENT '创建时间',
    MODIFY COLUMN `modified_time` datetime NULL COMMENT '修改时间',
    MODIFY COLUMN `create_by` varchar (30) NULL COMMENT '创建人',
    MODIFY COLUMN `modified_by` varchar (30) NULL COMMENT '修改人';

-- 22. room_type 表
ALTER TABLE `room_type`
    MODIFY COLUMN `create_time` datetime NULL COMMENT '创建时间',
    MODIFY COLUMN `modified_time` datetime NULL COMMENT '修改时间',
    MODIFY COLUMN `create_by` varchar (30) NULL COMMENT '创建人',
    MODIFY COLUMN `modified_by` varchar (30) NULL COMMENT '修改人';

-- 23. standard_group 表
ALTER TABLE `standard_group`
    MODIFY COLUMN `create_time` datetime NULL COMMENT '创建时间',
    MODIFY COLUMN `modified_time` datetime NULL COMMENT '修改时间',
    MODIFY COLUMN `create_by` varchar (30) NULL COMMENT '创建人',
    MODIFY COLUMN `modified_by` varchar (30) NULL COMMENT '修改人';

