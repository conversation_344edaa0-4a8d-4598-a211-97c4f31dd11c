package com.cw.controller.pms;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.CommonSelectReq;
import com.cw.pojo.dto.common.res.Common_Select_Res;
import com.cw.service.config.common.CustomDataService;
import com.cw.service.context.GlobalContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 公共数据获取
 */
@Api(tags = "公共数据获取")
@RestController
@RequestMapping(value = "/pmsapi/custom", method = RequestMethod.POST)
public class CustomDataController {
    @Autowired
    CustomDataService customDataService;

    @ApiOperation(value = "公共数据-获取下拉框数据", notes = "获取下拉框数据")
    @RequestMapping(value = "/select_data")
    public ResultJson<List<Common_Select_Res>> select_data(@RequestBody CommonSelectReq req) {
        return ResultJson.ok().data(customDataService.getSelectData(req, GlobalContext.getCurrentHotelId()));
    }


    @ApiOperation(value = "公共数据-获取付款方式下拉数据", notes = "获取付款方式下拉数据")
    @RequestMapping(value = "/select_bizpayment_data")
    public ResultJson<Common_Select_Res> select_bizpayment_data(@RequestBody CommonSelectReq req) {
        return ResultJson.ok().data(customDataService.getPaymentSelectData(req, GlobalContext.getCurrentHotelId()));
    }

    @ApiOperation(value = "公共数据-获取房间下拉数据", notes = "获取房间下拉数据")
    @RequestMapping(value = "/select_room_data")
    public ResultJson<Common_Select_Res> select_room_data(@RequestBody CommonSelectReq req) {
        return ResultJson.ok().data(customDataService.getRoomSelectData(req, GlobalContext.getCurrentHotelId()));
    }

    @ApiOperation(value = "公共数据-获取房型下拉数据", notes = "获取房型下拉数据")
    @RequestMapping(value = "/select_roomtype_data")
    public ResultJson<Common_Select_Res> select_roomtype_data(@RequestBody CommonSelectReq req) {
        return ResultJson.ok().data(customDataService.getRoomTypeSelectData(req, GlobalContext.getCurrentHotelId()));
    }

    @ApiOperation(value = "公共数据-获取按楼号分组的房型下拉数据", notes = "获取按楼号分组的房型下拉数据，包含ALL选项和各楼号的房型列表")
    @RequestMapping(value = "/select_roomtype_by_building_data")
    public ResultJson<List<SelectDataNode>> select_roomtype_by_building_data(@RequestBody CommonSelectReq req) {
        return ResultJson.ok().data(customDataService.getRoomTypeByBuildingSelectData(req, GlobalContext.getCurrentHotelId()));
    }

}
